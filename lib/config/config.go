package config

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/creasty/defaults"
	"github.com/go-playground/validator/v10"
	"github.com/go-viper/mapstructure/v2"
	"github.com/spf13/viper"
)

type Config[T any] struct {
	*viper.Viper
	opts *Options
	data T
}

type Options struct {
	File            string                        // absolute or relative path to the config file; if set, Name/Type/Paths are ignored
	Paths           []string                      // paths to search for the config file
	Name, Type      string                        // name of the config file (without extension) and type of the file
	EnvEnabled      bool                          // whether to use environment variables
	EnvPrefix       string                        // prefix for environment variables
	EnvKeyDelimiter string                        // delimiter for environment variables (defaults to _). Used with key replacer from . -> delimiter
	DecodeHookFuncs []mapstructure.DecodeHookFunc // hooks to decode the config file
}

// New creates a new typed configuration by reading from file(s), environment variables,
// applying defaults from `default` tags, and validating with `validate` or `validator` tags.
func New[T any](opts Options) (*Config[T], error) {
	v := viper.New()

	// Defaults
	if strings.TrimSpace(opts.Type) == "" {
		opts.Type = "yaml"
	}
	if len(opts.Paths) == 0 && strings.TrimSpace(opts.File) == "" {
		// Sensible defaults to search
		opts.Paths = []string{".", "./etc", "./etc/config", "./config"}
	}

	// Configure viper for file-based config
	if strings.TrimSpace(opts.File) != "" {
		v.SetConfigFile(opts.File)
	} else {
		if strings.TrimSpace(opts.Name) == "" {
			opts.Name = "config"
		}
		v.SetConfigName(opts.Name)
		v.SetConfigType(opts.Type)
		for _, p := range opts.Paths {
			if strings.TrimSpace(p) == "" {
				continue
			}
			v.AddConfigPath(p)
		}
	}

	// Environment variables
	if opts.EnvEnabled {
		v.AutomaticEnv()
		if strings.TrimSpace(opts.EnvPrefix) != "" {
			v.SetEnvPrefix(opts.EnvPrefix)
		}
		delimiter := opts.EnvKeyDelimiter
		if strings.TrimSpace(delimiter) == "" {
			delimiter = "_"
		}
		// Map nested keys like app.server.port -> APP_SERVER_PORT
		v.SetEnvKeyReplacer(strings.NewReplacer(".", delimiter))
	}

	// Read config file if present
	if err := readConfigIfExists(v); err != nil {
		return nil, err
	}

	// Compose decode hooks
	decodeHooks := []mapstructure.DecodeHookFunc{
		mapstructure.StringToTimeDurationHookFunc(),
		mapstructure.TextUnmarshallerHookFunc(),
	}
	if len(opts.DecodeHookFuncs) > 0 {
		decodeHooks = append(decodeHooks, opts.DecodeHookFuncs...)
	}
	composedHook := mapstructure.ComposeDecodeHookFunc(decodeHooks...)

	// Start with defaults from struct tags
	var data T
	if err := defaults.Set(&data); err != nil {
		return nil, fmt.Errorf("apply default tags: %w", err)
	}

	// Unmarshal into struct, overriding defaults with file/env values
	if err := v.Unmarshal(&data, viper.DecodeHook(composedHook)); err != nil {
		return nil, fmt.Errorf("unmarshal config: %w", err)
	}

	// Validate using `validate` tags (and also support `validator` tag name)
	if err := validateStruct(data); err != nil {
		return nil, err
	}

	return &Config[T]{
		Viper: v,
		opts:  &opts,
		data:  data,
	}, nil
}

// MustNew is like New but panics on error.
func MustNew[T any](opts Options) *Config[T] {
	c, err := New[T](opts)
	if err != nil {
		panic(err)
	}
	return c
}

// Value returns a copy of the current typed configuration value.
func (c *Config[T]) Value() T { return c.data }

// Reload re-reads config sources and re-applies defaults, decode hooks and validation.
func (c *Config[T]) Reload() error {
	if c == nil || c.Viper == nil {
		return errors.New("config is not initialized")
	}
	if err := readConfigIfExists(c.Viper); err != nil {
		return err
	}

	// Re-apply defaults, then unmarshal
	var data T
	if err := defaults.Set(&data); err != nil {
		return fmt.Errorf("apply default tags: %w", err)
	}

	decodeHooks := []mapstructure.DecodeHookFunc{
		mapstructure.StringToTimeDurationHookFunc(),
		mapstructure.TextUnmarshallerHookFunc(),
	}
	if c.opts != nil && len(c.opts.DecodeHookFuncs) > 0 {
		decodeHooks = append(decodeHooks, c.opts.DecodeHookFuncs...)
	}
	composedHook := mapstructure.ComposeDecodeHookFunc(decodeHooks...)

	if err := c.Unmarshal(&data, viper.DecodeHook(composedHook)); err != nil {
		return fmt.Errorf("unmarshal config: %w", err)
	}
	if err := validateStruct(data); err != nil {
		return err
	}
	c.data = data
	return nil
}

// Watch enables hot-reload. When the underlying config file changes, it reloads and invokes the callback.
// If callback is nil, it only reloads internal value.
func (c *Config[T]) Watch(onChange func(T)) {
	if c == nil || c.Viper == nil {
		return
	}
	c.WatchConfig()
	c.OnConfigChange(func(_ any) {
		if err := c.Reload(); err != nil {
			// Best-effort: ignore errors here; users can rely on explicit Reload().
			return
		}
		if onChange != nil {
			onChange(c.data)
		}
	})
}

// Helper: read config but don't fail if not found; only fail for real read/parse errors.
func readConfigIfExists(v *viper.Viper) error {
	if v == nil {
		return errors.New("nil viper")
	}
	if err := v.ReadInConfig(); err != nil {
		// If no config file is found, continue with defaults and env
		// viper returns ConfigFileNotFoundError for this case
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			return nil
		}
		// v1.20+ may wrap errors; try string match as a fallback
		if strings.Contains(strings.ToLower(err.Error()), "could not find config file") {
			return nil
		}
		return err
	}
	return nil
}

// validateStruct validates using go-playground/validator with `validate` tag name and also tries `validator`.
func validateStruct[T any](data T) error {
	v1 := validator.New(validator.WithRequiredStructEnabled())
	// Built-in time.Duration validation through custom tag isn't necessary; generally, duration is numeric or string decoded already.
	if err := v1.Struct(data); err != nil {
		// Try again using `validator` tag name for compatibility
		v2 := validator.New(validator.WithRequiredStructEnabled())
		v2.SetTagName("validator")
		if err2 := v2.Struct(data); err2 != nil {
			return wrapValidationError(err2)
		}
	}
	return nil
}

func wrapValidationError(err error) error {
	if err == nil {
		return nil
	}
	var verrs validator.ValidationErrors
	if errors.As(err, &verrs) {
		var b strings.Builder
		b.WriteString("config validation failed: ")
		for i, fe := range verrs {
			if i > 0 {
				b.WriteString(", ")
			}
			b.WriteString(fmt.Sprintf("%s %s", fe.Namespace(), humanizeTag(fe)))
		}
		return errors.New(b.String())
	}
	return err
}

func humanizeTag(fe validator.FieldError) string {
	switch fe.Tag() {
	case "required":
		return "is required"
	case "gt":
		return fmt.Sprintf("must be > %s", fe.Param())
	case "gte":
		return fmt.Sprintf("must be >= %s", fe.Param())
	case "lt":
		return fmt.Sprintf("must be < %s", fe.Param())
	case "lte":
		return fmt.Sprintf("must be <= %s", fe.Param())
	case "oneof":
		return fmt.Sprintf("must be one of [%s]", fe.Param())
	case "min":
		return fmt.Sprintf("min length %s", fe.Param())
	case "max":
		return fmt.Sprintf("max length %s", fe.Param())
	case "len":
		return fmt.Sprintf("length must be %s", fe.Param())
	case "email":
		return "must be a valid email"
	case "url":
		return "must be a valid URL"
	case "hostname":
		return "must be a valid hostname"
	case "duration":
		return "must be a valid duration"
	default:
		return fmt.Sprintf("failed %s", fe.Tag())
	}
}

// Prevent unused import error for time if hooks are adjusted in the future.
var _ = time.Second
