package json

import (
	"fmt"

	jsoniter "github.com/json-iterator/go"
)

var (
	json          = jsoniter.ConfigCompatibleWithStandardLibrary
	Marshal       = json.Marshal
	Unmarshal     = json.Unmarshal
	MarshalIndent = json.MarshalIndent
	NewDecoder    = json.NewDecoder
	NewEncoder    = json.NewEncoder
)

func MarshalToString(v any) string {
	s, err := jsoniter.MarshalToString(v)
	if err != nil {
		fmt.Println("Failed to marshal json string: " + err.<PERSON>rror())
		return ""
	}
	return s
}
